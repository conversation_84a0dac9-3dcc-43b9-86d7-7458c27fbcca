import asyncio
import json
import logging
import random
from dataclasses import dataclass
from typing import Dict, List, Optional

from playwright.async_api import (
    async_playwright,
    TimeoutError as PlaywrightTimeoutError,
    Error as PlaywrightError,
    ViewportSize
)

# 로깅 설정
import coloredlogs
from logging import LoggerAdapter

class ContextFilter(logging.Filter):
    def filter(self, record):
        record.proxy = getattr(record, 'proxy', 'N/A')
        record.device = getattr(record, 'device', 'N/A')
        return True

logger = logging.getLogger(__name__)
logger.addFilter(ContextFilter())

class ContextLogger:
    def __init__(self, logger, proxy=None, device=None):
        self.logger = logger
        self.proxy = proxy
        self.device = device
        
    def log(self, level, msg, *args, **kwargs):
        extra = kwargs.pop('extra', {})
        extra.update({'proxy': self.proxy, 'device': self.device})
        self.logger.log(level, msg, *args, extra=extra, **kwargs)
        
    def info(self, msg, *args, **kwargs):
        self.log(logging.INFO, msg, *args, **kwargs)
        
    def warning(self, msg, *args, **kwargs):
        self.log(logging.WARNING, msg, *args, **kwargs)
        
    def error(self, msg, *args, **kwargs):
        self.log(logging.ERROR, msg, *args, **kwargs)
        
    def debug(self, msg, *args, **kwargs):
        self.log(logging.DEBUG, msg, *args, **kwargs)

coloredlogs.install(
    level=logging.INFO,
    fmt='📅 [%(asctime)s] 🌐 [Proxy: %(proxy)s] 📱 [Device: %(device)s] %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    field_styles={
        'asctime': {'color': 'cyan'},
        'levelname': {'bold': True, 'color': 'black'},
        'proxy': {'color': 'blue'},
        'device': {'color': 'magenta'}
    },
    level_styles={
        'debug': {'color': 'green'},
        'info': {'color': 'white'},
        'warning': {'color': 'yellow'},
        'error': {'color': 'red'},
        'critical': {'color': 'red', 'bold': True}
    },
    handlers=[
        logging.FileHandler('naver_search.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

file_handler = next((h for h in logger.handlers if isinstance(h, logging.FileHandler)), None)
if not file_handler:
    file_handler = logging.FileHandler('naver_search.log', encoding='utf-8')
    logger.addHandler(file_handler)
file_handler.setFormatter(logging.Formatter(
    '[%(asctime)s] [Proxy: %(proxy)s] [Device: %(device)s] %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
))

@dataclass
class Config:
    PROXY_USERNAME: str = 'ogh3113'
    PROXY_PASSWORD: str = '@dlwnsdud0720'
    PROXY_FILE: str = "proxy.txt"
    UNFOUND_TARGETS_FILE: str = 'unfound_targets.json'
    NAVER_MOBILE_URL: str = "https://m.naver.com"
    SEARCH_INPUT_SELECTOR: str = '#MM_SEARCH_FAKE'
    MORE_RESULTS_BUTTON_SELECTOR: str = 'a.link_feed_more, a.group_more[href^="https://m.search.naver.com/search.naver?nso="]'
    PAGINATION_SELECTOR_TEMPLATE: str = 'a.pgn:has-text("{}")'
    PAGE_LOAD_TIMEOUT: int = 45000
    MIN_DELAY: float = 1.0
    MAX_DELAY: float = 5.0
    SEARCH_DELAY_MIN: float = 5.0
    SEARCH_DELAY_MAX: float = 10.0
    PROXY_CHANGE_DELAY_MIN: float = 10.0
    PROXY_CHANGE_DELAY_MAX: float = 20.0
    MAX_PAGINATION_PAGES: int = 10
    MIN_SCROLL_AMOUNT: int = 200
    MAX_SCROLL_AMOUNT: int = 800
    MIN_SCROLL_DURATION: float = 0.5
    MAX_SCROLL_DURATION: float = 2.0

@dataclass
class DeviceConfig:
    user_agent: str
    viewport: ViewportSize
    description: str = ""

config = Config()

TARGET_URL_SELECTORS = [
    'a.link_source:not(.txt_link):not(:has(.icon_nad_mark))',
    'a.lnk_url:not(.txt_link):not(:has(.icon_nad_mark))',
    'div.api_txt_lines a:not(.txt_link):not(:has(.icon_nad_mark))',
    'div.total_wrap a:not(.txt_link):not(:has(.icon_nad_mark))',
    'div.total_title a:not(.txt_link):not(:has(.icon_nad_mark))'
]

DEVICE_CONFIGS = [
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F946N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Fold 5 (Unfolded)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F731N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Flip 5'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-F936N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Fold 4 (Unfolded)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-F721N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Flip 4'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=393, height=852),
        description='iPhone 15'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=430, height=932),
        description='iPhone 15 Pro Max'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 14 Pro'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=428, height=926),
        description='iPhone 14 Plus'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.7 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=375, height=667),
        description='iPhone SE (3rd gen)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 14_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=414, height=896),
        description='iPhone 11'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-S928N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=384, height=854),
        description='Android 14 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-S911N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=360, height=800),
        description='Android 13 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F946N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=412, height=915),
        description='Z Fold 5 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F731N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=412, height=915),
        description='Z Flip 5 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1 KAKAOTALK',
        viewport=ViewportSize(width=393, height=852),
        description='iPhone 17 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1 KAKAOTALK',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 16 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1 KAKAOTALK',
        viewport=ViewportSize(width=428, height=926),
        description='iPhone 14 Plus KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1 KAKAOTALK',
        viewport=ViewportSize(width=430, height=932),
        description='iPhone 15 Pro Max KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-S928N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy S24 Ultra (wider viewport)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 11; SM-G998N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.101 Mobile Safari/537.36',
        viewport=ViewportSize(width=384, height=854),
        description='Galaxy S21 Ultra (Android 11)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 10; SM-A515N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Mobile Safari/537.36',
        viewport=ViewportSize(width=360, height=780),
        description='Galaxy A51 (Android 10)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-S901N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        viewport=ViewportSize(width=360, height=800),
        description='Galaxy S23'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 13 Pro (iOS 15)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=375, height=812),
        description='iPhone 11 Pro (iOS 14)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 14 Pro (iOS 16)'
    )
]

SEARCH_TARGETS = [
    {'keyword': '운암자이포레나', 'domain': 'aryatps.com'},
    {'keyword': '운암자이', 'domain': 'aryatps.com'},
    {'keyword': '일곡공원 위파크 1644-7240', 'domain': 'immodelhouse9.quv.kr'},
    {'keyword': '일곡위파크', 'domain': 'immodelhouse9.quv.kr'},
    {'keyword': '장성 삼계 서현위드184', 'domain': 'damyangkoreaadelium.quv.kr'},
    {'keyword': '장성 바울루체', 'domain': 'immodelhouse10.quv.kr'},
    {'keyword': '대전 엘크루', 'domain': 'immodelhouse3.quv.kr'},
    {'keyword': '정읍 월드메르디앙', 'domain': 'modelhouse7l7.quv.kr'},
    {'keyword': '송암공원 중흥s클래스', 'domain': 'immodelhouse98.quv.kr'},
    {'keyword': '중앙공원 롯데캐슬', 'domain': 'immodelhouse2. quv.kr'},
    {'keyword': '함평 미래프레지안', 'domain': 'modelhouse1d.quv.kr'},
    {'keyword': '봉선 이편한세상', 'domain': 'modelhouse1b.quv.kr'},
    {'keyword': '함평 서현 수와일 리버파크', 'domain': 'immodelhouse7.quv.kr'},
    {'keyword': '동림 우방아이유쉘', 'domain': 'modelhouse2b.quv.kr'},
    {'keyword': '동림2차 우방아이유쉘', 'domain': 'modelhouse2b.quv.kr'},
    {'keyword': '무등산자이앤어울림', 'domain': 'kingofyeosuhonors.quv.kr'},
    {'keyword': '광양 푸르지오 센터파크', 'domain': 'modelhouse1g.quv.kr'},
    {'keyword': '더샵광양레이크센텀', 'domain': 'immodelhouse1.quv.kr'},
    {'keyword': '상무 스위첸', 'domain': 'immodelhouse81.quv.kr'},
    {'keyword': '마포 빌리브디에이블', 'domain': 'immodelhouse96.quv.kr'},
    {'keyword': '마포빌리브에이블', 'domain': 'immodelhouse96.quv.kr'},
    {'keyword': '상무 모아미래도', 'domain': 'immodelhouse93.quv.kr'},
    {'keyword': '순천 더포레스트 마루힐', 'domain': 'immodelhouseb.quv.kr'},
    {'keyword': '무인카페 스타벅스', 'domain': 'starbuckskorea.quv.kr'},
    {'keyword': '중외공원 힐스테이트 공식1644-7240', 'domain': 'immodelhome999.quv.kr'},
    {'keyword': '힐스테이트 중외공원 공식', 'domain': 'immodelhome999.quv.kr'},
    {'keyword': '순천 지에이그린웰 예약', 'domain': 'immodelhouse90.quv.kr'},
    {'keyword': '선운2지구 예다음', 'domain': 'goldmodelhouse.quv.kr'},
    {'keyword': '아크로베스티뉴 공식', 'domain': 'inmodelhouse.quv.kr'},
    {'keyword': '광주 한양더힐 공식', 'domain': 'modelhouse1c.quv.kr'},
    {'keyword': '각화 한양더힐1644-7240', 'domain': 'modelhouse1c.quv.kr'},
    {'keyword': '월산 힐스테이트1644 7240', 'domain': 'immodelhouse4.quv.kr'},
    {'keyword': '중앙공원 위파크', 'domain': 'immodelhouse99.quv.kr'},
    {'keyword': '화정 두산위브 모델하우스', 'domain': 'modelhouse1a.quv.kr'},
    {'keyword': '익산역 유탑유블레스', 'domain': 'modelhouse1e.quv.kr'},
    {'keyword': '진월 더리브 라포레', 'domain': 'iamodelhome.quv.kr'},
    {'keyword': '무등산 우방아이유쉘', 'domain': 'thesynergy.quv.kr'},
    {'keyword': '힐스테이트 천호역1644-7240', 'domain': 'immodelhouse91.quv.kr'}
]

async def load_proxies(file_path: str) -> List[str]:
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            proxies = [line.strip() for line in f if line.strip()]
        logger.info(f"{len(proxies)}개의 프록시를 '{file_path}'에서 로드했습니다.")
        return proxies
    except FileNotFoundError:
        logger.error(f"오류: '{file_path}' 파일을 찾을 수 없습니다.")
        return []
    except Exception as e:
        logger.error(f"프록시 로드 중 오류 발생: {e}")
        return []

async def human_like_delay(min_seconds: Optional[float] = None, max_seconds: Optional[float] = None):
    if min_seconds is None:
        min_seconds = config.MIN_DELAY
    if max_seconds is None:
        max_seconds = config.MAX_DELAY
    delay_time = random.uniform(min_seconds, max_seconds)
    logger.debug(f"'{delay_time:.2f}'초 동안 대기합니다.")
    await asyncio.sleep(delay_time)

async def random_scroll(page):
    scroll_amount = random.randint(config.MIN_SCROLL_AMOUNT, config.MAX_SCROLL_AMOUNT)
    scroll_duration = random.uniform(config.MIN_SCROLL_DURATION, config.MAX_SCROLL_DURATION)
    logger.debug(f"'{scroll_amount}' 픽셀만큼 스크롤하고 '{scroll_duration:.2f}'초 대기합니다.")
    await page.evaluate(f"window.scrollBy(0, {scroll_amount})")
    await asyncio.sleep(scroll_duration)

async def visit_target_url(page, target_url: str) -> str:
    logger.info(f"목표 URL 방문: {target_url}")
    try:
        await human_like_delay(2, 5)
        for attempt in range(3):
            try:
                await page.goto(target_url, wait_until='domcontentloaded', timeout=config.PAGE_LOAD_TIMEOUT)
                break
            except Exception as e:
                if attempt == 2:
                    raise
                logger.warning(f"{attempt+1}차 재시도 중... 오류: {repr(e)}")
                await asyncio.sleep(5 * (attempt + 1))
        await human_like_delay(5, 10)
        logger.info("목표 URL 방문 완료.")
        return target_url
    except PlaywrightTimeoutError:
        logger.warning(f"목표 URL '{target_url}' 방문 시간 초과 (TimeoutError).")
        return target_url
    except PlaywrightError as e:
        logger.error(f"목표 URL '{target_url}' 방문 중 Playwright 오류 발생: {e}.")
        return target_url
    except Exception as e:
        logger.error(f"목표 URL '{target_url}' 방문 중 예상치 못한 오류 발생: {e}.")
        return target_url

async def load_page_and_scroll(page, action_description: str) -> None:
    logger.info(f"{action_description} 후 결과 로딩을 위해 대기 중...")
    await human_like_delay(5, 8)
    await random_scroll(page)
    logger.info(f"{action_description} 완료.")

async def return_to_naver_home(page, context_logger, reason: str, keyword: str) -> bool:
    context_logger.info(f"{reason}을 위해 네이버 모바일 페이지 '{config.NAVER_MOBILE_URL}'로 이동.")
    try:
        await page.goto(config.NAVER_MOBILE_URL, timeout=config.PAGE_LOAD_TIMEOUT, wait_until='networkidle')
        await human_like_delay()
        context_logger.info("네이버 모바일 페이지 로드 완료.")
        return True
    except PlaywrightTimeoutError:
        context_logger.error(f"네이버 모바일 페이지 로드 시간 초과 (TimeoutError) {reason}. 키워드: '{keyword}'")
        return False
    except PlaywrightError as e:
        context_logger.error(f"네이버 모바일 페이지 로드 중 Playwright 오류 발생: {e} {reason}. 키워드: '{keyword}'")
        return False
    except Exception as e:
        context_logger.error(f"네이버 모바일 페이지 로드 중 예상치 못한 오류 발생: {e} {reason}. 키워드: '{keyword}'")
        return False

async def search_and_visit_on_page(page, domain: str, page_description: str, keyword: str) -> Optional[str]:
    logger.info(f"{page_description}에서 목표 URL 검색 시작")
    logger.debug(f"대상 도메인: {domain}, 키워드: {keyword}")
    try:
        target_url = await find_target_url(page, domain)
        if target_url:
            logger.info(f"{page_description}에서 목표 URL 성공적으로 발견: {target_url}")
            return await visit_target_url(page, target_url)
        logger.info(f"{page_description}에서 목표 URL을 찾지 못함")
        return None
    except Exception as e:
        logger.error(f"{page_description} 검색 중 오류 발생: {str(e)}")
        return None

async def find_target_url(page, domain: str) -> Optional[str]:
    logger.debug(f"도메인 '{domain}'을 포함하는 목표 URL 검색 시작.")
    for selector in TARGET_URL_SELECTORS:
        try:
            links = await page.query_selector_all(selector)
            for link in links:
                href = await link.get_attribute('href')
                if href and domain in href:
                    logger.info(f"목표 URL 발견: '{href}'")
                    return href
        except PlaywrightError as e:
            logger.warning(f"셀렉터 '{selector}' 처리 중 Playwright 오류 발생: {e}")
            continue
        except Exception as e:
            logger.warning(f"셀렉터 '{selector}' 처리 중 예상치 못한 오류 발생: {e}")
            continue
    logger.debug(f"도메인 '{domain}'을 포함하는 목표 URL을 찾지 못했습니다.")
    return None

async def search_naver(page, keyword: str, domain: str) -> Optional[str]:
    logger.info(f"'{keyword}' 검색어와 도메인 '{domain}'으로 검색 시작")
    try:
        try:
            await page.fill(config.SEARCH_INPUT_SELECTOR, keyword)
            await human_like_delay(1, 2)
            await page.press(config.SEARCH_INPUT_SELECTOR, 'Enter')
            logger.info("검색 실행 후 결과 로딩을 위해 대기 중...")
            await human_like_delay(7, 10)
            await random_scroll(page)
            logger.info("검색 실행 및 대기 완료. 목표 URL 탐색 시작.")
        except PlaywrightError as e:
            logger.error(f"검색 실행 중 Playwright 오류 발생: {e}. 키워드: '{keyword}'")
            return None
        except Exception as e:
            logger.error(f"검색 실행 중 예상치 못한 오류 발생: {e}. 키워드: '{keyword}'")
            return None

        target_url = await search_and_visit_on_page(page, domain, "1페이지", keyword)
        if target_url:
            return target_url

        more_results_button = await page.query_selector(config.MORE_RESULTS_BUTTON_SELECTOR)
        if more_results_button:
            try:
                await more_results_button.click()
                await load_page_and_scroll(page, "'검색결과 더보기' 클릭")
                target_url = await search_and_visit_on_page(page, domain, "2페이지", keyword)
                if target_url:
                    return target_url
            except Exception:
                pass
        else:
            pagination_selector_2 = config.PAGINATION_SELECTOR_TEMPLATE.format(2)
            pagination_button_2 = await page.query_selector(pagination_selector_2)
            if pagination_button_2:
                try:
                    await pagination_button_2.click()
                    await load_page_and_scroll(page, "페이지네이션을 사용하여 2페이지")
                    target_url = await search_and_visit_on_page(page, domain, "2페이지", keyword)
                    if target_url:
                        return target_url
                except Exception:
                    pass

        for i in range(3, config.MAX_PAGINATION_PAGES + 1):
            pagination_selector = config.PAGINATION_SELECTOR_TEMPLATE.format(i)
            pagination_button = await page.query_selector(pagination_selector)
            if pagination_button:
                try:
                    await pagination_button.click()
                    await load_page_and_scroll(page, f"{i} 페이지")
                    target_url = await search_and_visit_on_page(page, domain, f"{i} 페이지", keyword)
                    if target_url:
                        return target_url
                except Exception:
                    continue
            else:
                break

        logger.info(f"'{keyword}' 검색어와 도메인 '{domain}'에 대한 목표 URL을 찾지 못했습니다.")
        return None
    except Exception as e:
        logger.error(f"'{keyword}' 검색어와 도메인 '{domain}' 검색 중 치명적인 오류 발생: {e}")
        return None

def save_unfound_targets(unfound_targets: List[Dict], file_path: str) -> None:
    if unfound_targets:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(unfound_targets, f, ensure_ascii=False, indent=4)
            logger.info(f"목표 URL을 찾지 못한 검색 설정 {len(unfound_targets)}개를 '{file_path}' 파일에 저장했습니다.")
        except Exception as e:
            logger.error(f"찾지 못한 검색 설정 저장 중 오류 발생: {e}")
    else:
        logger.info("목표 URL을 찾지 못한 검색 설정이 없습니다.")

async def main() -> None:
    proxies = await load_proxies(config.PROXY_FILE)
    if not proxies:
        logger.error("프록시 목록이 비어 있습니다. 프로그램을 종료합니다.")
        return

    unfound_targets = []
    async with async_playwright() as p:
        for proxy in proxies:
            from playwright.async_api import ProxySettings
            proxy_config = ProxySettings(
                server=proxy,
                username=config.PROXY_USERNAME,
                password=config.PROXY_PASSWORD
            )
            browser = None
            try:
                browser = await p.chromium.launch(
                    headless=False,
                    proxy=proxy_config,
                    args=[
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-blink-features=AutomationControlled'
                    ]
                )
                random_device_raw = random.choice(DEVICE_CONFIGS)
                if isinstance(random_device_raw, dict):
                    viewport_data = random_device_raw.get('viewport', {'width': 0, 'height': 0})
                    random_device = DeviceConfig(
                        user_agent=random_device_raw.get('user_agent', ''),
                        viewport=ViewportSize(**viewport_data),
                        description=random_device_raw.get('description', 'Unknown Device')
                    )
                else:
                    random_device = random_device_raw

                user_agent = random_device.user_agent
                viewport = random_device.viewport
                description = random_device.description
                
                context_logger = ContextLogger(logger, proxy=proxy, device=description)
                context_logger.info(f"선택된 장치 설정: {description} - User-Agent='{user_agent}', Viewport='{viewport}'")

                # 전역 logger 대신 context_logger를 사용하도록 함수에 전달
                global logger
                original_logger = logger
                logger = context_logger

                context = await browser.new_context(
                    user_agent=user_agent,
                    viewport=viewport,
                    locale='ko-KR',
                    permissions=['geolocation'],
                    ignore_https_errors=True
                )
                page = await context.new_page()

                try:
                    await page.goto(config.NAVER_MOBILE_URL, timeout=config.PAGE_LOAD_TIMEOUT, wait_until='networkidle')
                    await human_like_delay()
                except Exception as e:
                    logger.error(f"네이버 모바일 페이지 로드 실패: {e}")
                    continue

                search_targets_shuffled = SEARCH_TARGETS.copy()
                random.shuffle(search_targets_shuffled)

                for target in search_targets_shuffled:
                    keyword = target.get('keyword', '')
                    domain = target.get('domain', '')
                    if not keyword or not domain:
                        continue

                    logger.info(f"새 작업 시작: 키워드 '{keyword}', 도메인 '{domain}'.")
                    if not await return_to_naver_home(page, context_logger, "새 작업 시작", keyword):
                        unfound_targets.append(target)
                        continue

                    found = await search_naver(page, keyword, domain)
                    if found:
                        logger.info(f"성공: 키워드 '{keyword}', 도메인 '{domain}'에 대한 목표 URL 발견 및 방문 완료.")
                        if not await return_to_naver_home(page, context_logger, "다음 검색", keyword):
                            break
                    else:
                        logger.warning(f"실패: 키워드 '{keyword}', 도메인 '{domain}'에 대한 목표 URL을 찾지 못했습니다.")
                        unfound_targets.append(target)
                        if not await return_to_naver_home(page, context_logger, "다음 검색", keyword):
                            break

                    await human_like_delay(config.SEARCH_DELAY_MIN, config.SEARCH_DELAY_MAX)
            finally:
                if browser:
                    await browser.close()
            await human_like_delay(config.PROXY_CHANGE_DELAY_MIN, config.PROXY_CHANGE_DELAY_MAX)

    save_unfound_targets(unfound_targets, config.UNFOUND_TARGETS_FILE)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("사용자에 의해 프로그램이 중단되었습니다.")
    except Exception as e:
        logger.error(f"프로그램 실행 중 예상치 못한 오류 발생: {e}")
    finally:
        logger.info("프로그램 종료.")
